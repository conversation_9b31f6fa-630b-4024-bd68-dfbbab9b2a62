"use strict";(self.webpackChunkcurlygirlytravely=self.webpackChunkcurlygirlytravely||[]).push([[2750],{3041:(e,n,r)=>{r.r(n),r.d(n,{assets:()=>o,contentTitle:()=>a,default:()=>h,frontMatter:()=>s,metadata:()=>i,toc:()=>c});const i=JSON.parse('{"type":"mdx","permalink":"/about","source":"@site/src/pages/about.md","title":"About CurlyGirlyTravely","description":"Welcome to CurlyGirlyTravely! I\'m <PERSON><PERSON><PERSON>, an engineer, explorer, and wellness seeker passionate about sharing travel experiences and mindful living tips.","frontMatter":{},"unlisted":false}');var l=r(4848),t=r(8453);const s={},a="About CurlyGirlyTravely",o={},c=[{value:"About Me",id:"about-me",level:2},{value:"What You&#39;ll Find Here",id:"what-youll-find-here",level:2},{value:"My Journey",id:"my-journey",level:2},{value:"Connect With Me",id:"connect-with-me",level:2}];function d(e){const n={a:"a",h1:"h1",h2:"h2",header:"header",li:"li",p:"p",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(n.header,{children:(0,l.jsx)(n.h1,{id:"about-curlygirlytravely",children:"About CurlyGirlyTravely"})}),"\n",(0,l.jsx)(n.p,{children:"Welcome to CurlyGirlyTravely! I'm Shilpa Mandara, an engineer, explorer, and wellness seeker passionate about sharing travel experiences and mindful living tips."}),"\n",(0,l.jsx)(n.h2,{id:"about-me",children:"About Me"}),"\n",(0,l.jsx)(n.p,{children:"My name is Shilpa! I am an engineer, explorer, and wellness seeker. Through this blog, you can discover slow travel guides, healthy vegetarian food tips, and mindful living tips with me."}),"\n",(0,l.jsx)(n.h2,{id:"what-youll-find-here",children:"What You'll Find Here"}),"\n",(0,l.jsxs)(n.ul,{children:["\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Travel Guides"}),": Detailed guides to beautiful destinations across India and beyond"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Wellness Tips"}),": Advice on staying fit and healthy while traveling"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Mindful Living"}),": Tips for incorporating mindfulness into your daily life"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Photography"}),": Capturing the beauty of our journeys"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Personal Stories"}),": Authentic travel experiences and life lessons"]}),"\n"]}),"\n",(0,l.jsx)(n.h2,{id:"my-journey",children:"My Journey"}),"\n",(0,l.jsx)(n.p,{children:"I believe in exploring the world with curiosity, respect, and mindfulness. Whether it's a weekend getaway to a nearby dam, a trek in the Himalayas, or a cultural experience in Rajasthan, every journey has something to teach us."}),"\n",(0,l.jsx)(n.h2,{id:"connect-with-me",children:"Connect With Me"}),"\n",(0,l.jsx)(n.p,{children:"Feel free to reach out and share your own travel stories! I love connecting with fellow travelers and learning about new destinations."}),"\n",(0,l.jsxs)(n.ul,{children:["\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Instagram"}),": ",(0,l.jsx)(n.a,{href:"https://instagram.com/curlygirlytravely",children:"@curlygirlytravely"})]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Facebook"}),": ",(0,l.jsx)(n.a,{href:"https://facebook.com/curlygirlytravely",children:"CurlyGirlyTravely"})]}),"\n"]}),"\n",(0,l.jsx)(n.p,{children:"Thank you for joining me on this journey of exploration and discovery!"})]})}function h(e={}){const{wrapper:n}={...(0,t.R)(),...e.components};return n?(0,l.jsx)(n,{...e,children:(0,l.jsx)(d,{...e})}):d(e)}},8453:(e,n,r)=>{r.d(n,{R:()=>s,x:()=>a});var i=r(6540);const l={},t=i.createContext(l);function s(e){const n=i.useContext(t);return i.useMemo(function(){return"function"==typeof e?e(n):{...n,...e}},[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(l):e.components||l:s(e.components),i.createElement(t.Provider,{value:n},e.children)}}}]);