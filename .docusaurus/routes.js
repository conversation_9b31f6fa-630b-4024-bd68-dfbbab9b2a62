import React from 'react';
import ComponentCreator from '@docusaurus/ComponentCreator';

export default [
  {
    path: '/__docusaurus/debug',
    component: ComponentCreator('/__docusaurus/debug', '5ff'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/config',
    component: ComponentCreator('/__docusaurus/debug/config', '5ba'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/content',
    component: ComponentCreator('/__docusaurus/debug/content', 'a2b'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/globalData',
    component: ComponentCreator('/__docusaurus/debug/globalData', 'c3c'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/metadata',
    component: ComponentCreator('/__docusaurus/debug/metadata', '156'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/registry',
    component: ComponentCreator('/__docusaurus/debug/registry', '88c'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/routes',
    component: ComponentCreator('/__docusaurus/debug/routes', '000'),
    exact: true
  },
  {
    path: '/about',
    component: ComponentCreator('/about', '954'),
    exact: true
  },
  {
    path: '/blog',
    component: ComponentCreator('/blog', 'a27'),
    exact: true
  },
  {
    path: '/blog/8-simple-ways-to-stay-fit-while-you-travel',
    component: ComponentCreator('/blog/8-simple-ways-to-stay-fit-while-you-travel', 'e20'),
    exact: true
  },
  {
    path: '/blog/archive',
    component: ComponentCreator('/blog/archive', '182'),
    exact: true
  },
  {
    path: '/blog/authors',
    component: ComponentCreator('/blog/authors', '0b7'),
    exact: true
  },
  {
    path: '/blog/authors/shilpa',
    component: ComponentCreator('/blog/authors/shilpa', '006'),
    exact: true
  },
  {
    path: '/blog/authors/shilpa/authors/2',
    component: ComponentCreator('/blog/authors/shilpa/authors/2', '993'),
    exact: true
  },
  {
    path: '/blog/chokhi-dhani-jaipur-a-cultural-evening-experience',
    component: ComponentCreator('/blog/chokhi-dhani-jaipur-a-cultural-evening-experience', '72c'),
    exact: true
  },
  {
    path: '/blog/dawn-to-dusk-silhouette-stories-from-kasarsai-dam',
    component: ComponentCreator('/blog/dawn-to-dusk-silhouette-stories-from-kasarsai-dam', '84c'),
    exact: true
  },
  {
    path: '/blog/from-reality-to-fantasy-ghibli-art-of-my-himalayan-adventures',
    component: ComponentCreator('/blog/from-reality-to-fantasy-ghibli-art-of-my-himalayan-adventures', '705'),
    exact: true
  },
  {
    path: '/blog/harihar-fort-trek-the-not-so-dangerous-trek-in-india',
    component: ComponentCreator('/blog/harihar-fort-trek-the-not-so-dangerous-trek-in-india', '388'),
    exact: true
  },
  {
    path: '/blog/hidden-trail-near-koyna-river',
    component: ComponentCreator('/blog/hidden-trail-near-koyna-river', '8bf'),
    exact: true
  },
  {
    path: '/blog/khadakwasla-dam-by-bicycle-scenic-views-ice-gola-cravings',
    component: ComponentCreator('/blog/khadakwasla-dam-by-bicycle-scenic-views-ice-gola-cravings', '5ad'),
    exact: true
  },
  {
    path: '/blog/luxury-in-the-land-of-high-passes-my-ladakh-staycation-experience',
    component: ComponentCreator('/blog/luxury-in-the-land-of-high-passes-my-ladakh-staycation-experience', '378'),
    exact: true
  },
  {
    path: '/blog/night-trek-to-kalavantin-durg',
    component: ComponentCreator('/blog/night-trek-to-kalavantin-durg', '78f'),
    exact: true
  },
  {
    path: '/blog/our-babymoon-experience-in-india',
    component: ComponentCreator('/blog/our-babymoon-experience-in-india', '98b'),
    exact: true
  },
  {
    path: '/blog/page/2',
    component: ComponentCreator('/blog/page/2', '2f1'),
    exact: true
  },
  {
    path: '/blog/peaceful-mornings-at-the-pune-okayama-friendship-garden',
    component: ComponentCreator('/blog/peaceful-mornings-at-the-pune-okayama-friendship-garden', '6d5'),
    exact: true
  },
  {
    path: '/blog/ramdara-temple-in-loni-kalbhor-a-peaceful-escape-near-pune',
    component: ComponentCreator('/blog/ramdara-temple-in-loni-kalbhor-a-peaceful-escape-near-pune', '2d7'),
    exact: true
  },
  {
    path: '/blog/tags',
    component: ComponentCreator('/blog/tags', '287'),
    exact: true
  },
  {
    path: '/blog/tags/blog',
    component: ComponentCreator('/blog/tags/blog', '461'),
    exact: true
  },
  {
    path: '/blog/tags/blog/page/2',
    component: ComponentCreator('/blog/tags/blog/page/2', '44a'),
    exact: true
  },
  {
    path: '/blog/tags/travel',
    component: ComponentCreator('/blog/tags/travel', '5c5'),
    exact: true
  },
  {
    path: '/blog/tags/travel/page/2',
    component: ComponentCreator('/blog/tags/travel/page/2', '419'),
    exact: true
  },
  {
    path: '/blog/the-ultimate-guide-to-exploring-rishikesh-spirituality-adventure-and-serenity',
    component: ComponentCreator('/blog/the-ultimate-guide-to-exploring-rishikesh-spirituality-adventure-and-serenity', 'cf5'),
    exact: true
  },
  {
    path: '/blog/top-10-best-cafes-in-manali-for-food-views-and-vibes',
    component: ComponentCreator('/blog/top-10-best-cafes-in-manali-for-food-views-and-vibes', '763'),
    exact: true
  },
  {
    path: '/blog/two-days-in-mussoorie-must-visit-places-travel-tips',
    component: ComponentCreator('/blog/two-days-in-mussoorie-must-visit-places-travel-tips', 'a8f'),
    exact: true
  },
  {
    path: '/markdown-page',
    component: ComponentCreator('/markdown-page', '3d7'),
    exact: true
  },
  {
    path: '/docs',
    component: ComponentCreator('/docs', '733'),
    routes: [
      {
        path: '/docs',
        component: ComponentCreator('/docs', '7e6'),
        routes: [
          {
            path: '/docs',
            component: ComponentCreator('/docs', 'ba6'),
            routes: [
              {
                path: '/docs/category/tutorial---basics',
                component: ComponentCreator('/docs/category/tutorial---basics', '20e'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/category/tutorial---extras',
                component: ComponentCreator('/docs/category/tutorial---extras', '9ad'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/intro',
                component: ComponentCreator('/docs/intro', '61d'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/tutorial-basics/congratulations',
                component: ComponentCreator('/docs/tutorial-basics/congratulations', '458'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/tutorial-basics/create-a-blog-post',
                component: ComponentCreator('/docs/tutorial-basics/create-a-blog-post', '108'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/tutorial-basics/create-a-document',
                component: ComponentCreator('/docs/tutorial-basics/create-a-document', '8fc'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/tutorial-basics/create-a-page',
                component: ComponentCreator('/docs/tutorial-basics/create-a-page', '951'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/tutorial-basics/deploy-your-site',
                component: ComponentCreator('/docs/tutorial-basics/deploy-your-site', '4f5'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/tutorial-basics/markdown-features',
                component: ComponentCreator('/docs/tutorial-basics/markdown-features', 'b05'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/tutorial-extras/manage-docs-versions',
                component: ComponentCreator('/docs/tutorial-extras/manage-docs-versions', '978'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/tutorial-extras/translate-your-site',
                component: ComponentCreator('/docs/tutorial-extras/translate-your-site', 'f9a'),
                exact: true,
                sidebar: "tutorialSidebar"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/',
    component: ComponentCreator('/', 'e5f'),
    exact: true
  },
  {
    path: '*',
    component: ComponentCreator('*'),
  },
];
